{"app": {"bundleName": "com.example.neteasy_cloud", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:app_icon", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50002014, "minAPIVersion": 40000010, "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug", "debug": true, "iconId": 16777216, "labelId": 16777217}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ts", "description": "$string:ability_desc", "icon": "$media:icon", "label": "$string:ability_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777229, "iconId": 16777226, "labelId": 16777230, "startWindowIconId": 16777226, "startWindowBackgroundId": 16777224, "ohmurl": "@normalized:N&undefined&undefined&undefined/src/main/ets/entryability/EntryAbility&undefined"}], "requestPermissions": [{"name": "ohos.permission.INTERNET"}], "packageName": "entry", "virtualMachine": "ark9.0.0.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777234}}