if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface DailyRecommendPage_Params {
    titleParam?: Resource;
}
import router from "@ohos:router";
import { CommonConstants } from "@bundle:com.example.neteasy_cloud/entry/ets/common/constants/CommonConstants";
class DailyRecommendPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.titleParam = { "id": 16777231, "type": 10003, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" };
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: DailyRecommendPage_Params) {
        if (params.titleParam !== undefined) {
            this.titleParam = params.titleParam;
        }
    }
    updateStateVars(params: DailyRecommendPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private titleParam: Resource;
    aboutToAppear() {
        if (router.getParams()) {
            const params = router.getParams() as Record<string, Object>;
            this.titleParam = params.data as Resource;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DailyRecommendPage.ets(21:5)", "entry");
            Column.width('100%');
            Column.height(CommonConstants.DETAIL_COLUMN_HEIGHT_PERCENT);
            Column.backgroundColor({ "id": 16777225, "type": 10001, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777275, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/DailyRecommendPage.ets(22:7)", "entry");
            Image.width('62');
            Image.height('62');
            Image.borderRadius('32');
            Image.margin({ top: 134 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DailyRecommendPage.ets(27:7)", "entry");
            Column.justifyContent(FlexAlign.Center);
            Column.width('100%');
            Column.position({
                x: 0,
                y: '80%'
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('手机号登录');
            Button.debugLine("entry/src/main/ets/pages/DailyRecommendPage.ets(28:9)", "entry");
            Button.width(282);
            Button.height(38);
            Button.borderRadius(50);
            Button.fontColor('#C20C0C');
            Button.backgroundColor('#fff');
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('立即体验');
            Button.debugLine("entry/src/main/ets/pages/DailyRecommendPage.ets(34:9)", "entry");
            Button.width(282);
            Button.height(38);
            Button.borderRadius(50);
            Button.fontColor('#fff');
            Button.backgroundColor('transparent');
            Button.border({
                width: 1,
                color: "#fff"
            });
            Button.margin({
                top: 25
            });
            Button.onClick(() => {
                router.pushUrl({
                    url: CommonConstants.INDEX_PAGE
                });
                console.log('ok');
            });
        }, Button);
        Button.pop();
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "DailyRecommendPage";
    }
}
registerNamedRoute(() => new DailyRecommendPage(undefined, {}), "", { bundleName: "com.example.neteasy_cloud", moduleName: "entry", pagePath: "pages/DailyRecommendPage", pageFullPath: "entry/src/main/ets/pages/DailyRecommendPage", integratedHsp: "false", moduleType: "followWithHap" });
