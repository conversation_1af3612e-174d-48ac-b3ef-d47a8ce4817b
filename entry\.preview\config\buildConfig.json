{"deviceType": "phone,tablet", "checkEntry": "true", "localPropertiesPath": "D:\\Code\\harmony\\open_neteasy_cloud\\local.properties", "Path": "D:\\IDE\\DevEco Studio\\tools\\node\\", "note": "false", "aceProfilePath": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "isPreview": "true", "hapMode": "false", "buildMode": "debug", "img2bin": "true", "projectProfilePath": "D:\\Code\\harmony\\open_neteasy_cloud\\build-profile.json5", "watchMode": "true", "appResource": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "logLevel": "3", "stageRouterConfig": {"contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/HomePage\",\"pages/IndexPage\",\"pages/DailyRecommendPage\"]}"], "paths": ["D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"]}, "port": "29903", "aceBuildJson": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\src\\main\\ets", "aceSoPath": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\Code\\harmony\\open_neteasy_cloud\\entry\\.preview\\default\\intermediates\\res\\default\\module.json"}