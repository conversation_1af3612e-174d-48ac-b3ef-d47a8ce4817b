   ability_desc   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU  !   This ability loads Category List.
   ability_label   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU     Category List Ability   detail_default_title   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU  
   Default title   list_item_summary   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU  
   Right text   list_item_title   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU     Single list   module_desc   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU  8   This module template implements Category List functions.
   page_title   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU     Title	   sub_title   en_USU   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\en_US\element\string.json	          ne     SU     Subtitle