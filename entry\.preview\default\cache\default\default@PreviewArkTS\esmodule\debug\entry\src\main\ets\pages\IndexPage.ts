if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface IndexPage_Params {
    titleParam?: Resource;
    controller?: TabsController;
    swiperController?: SwiperController;
    currentIndex?: number;
}
import router from "@ohos:router";
class IndexPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.titleParam = { "id": 16777231, "type": 10003, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" };
        this.controller = new TabsController();
        this.swiperController = new SwiperController();
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: IndexPage_Params) {
        if (params.titleParam !== undefined) {
            this.titleParam = params.titleParam;
        }
        if (params.controller !== undefined) {
            this.controller = params.controller;
        }
        if (params.swiperController !== undefined) {
            this.swiperController = params.swiperController;
        }
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
    }
    updateStateVars(params: IndexPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private titleParam: Resource;
    private controller: TabsController;
    private swiperController: SwiperController;
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    aboutToAppear() {
        if (router.getParams()) {
            const params = router.getParams() as Record<string, Object>;
            this.titleParam = params.data as Resource;
        }
    }
    TabTextBuilder(text: string, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(text);
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(24:5)", "entry");
            Text.fontSize(this.currentIndex === index ? 18 : 16);
            Text.fontColor(this.currentIndex === index ? '#000' : '#888');
            Text.fontWeight(this.currentIndex === index ? 800 : 400);
        }, Text);
        Text.pop();
    }
    TabMyContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/IndexPage.ets(31:5)", "entry");
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(32:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceAround, alignItems: ItemAlign.Center });
            Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(33:9)", "entry");
            Flex.height(86);
        }, Flex);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(34:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(35:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('私人FM');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(39:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(41:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(42:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('心动模式');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(46:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(48:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(49:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('私人电台');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(53:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(55:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(56:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('跑步FM');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(60:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(62:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(63:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('心动模式');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(67:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        Flex.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(71:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(72:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(73:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('本地音乐');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(76:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(86:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(87:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近播放');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(90:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(100:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(101:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('下载管理');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(104:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(114:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(115:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的电台');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(118:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(128:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(129:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的收藏');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(132:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/IndexPage.ets(143:9)", "entry");
            Divider.strokeWidth(9);
            Divider.color('#dcdcdc');
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(146:9)", "entry");
            Column.width('100%');
            Column.padding({ left: 13, top: 6 });
            Column.alignItems(HorizontalAlign.Start);
            Column.justifyContent(FlexAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('创建的歌单');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(147:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.fontWeight(800);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(152:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create('http://p1.music.126.net/mENtBPo_dYhMgsv0_3ae9A==/109951165448009459.jpg');
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(153:13)", "entry");
            Image.width(56);
            Image.height(56);
            Image.margin({ right: 12 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(157:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('云烟成雨喜欢的音乐');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(158:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('14首');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(161:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(178:5)", "entry");
            Column.justifyContent(FlexAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777227, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(179:9)", "entry");
            Image.width(22);
            Image.height(22);
            Image.position({
                x: 10,
                y: 14
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ index: this.currentIndex, controller: this.controller });
            Tabs.debugLine("entry/src/main/ets/pages/IndexPage.ets(186:9)", "entry");
            Tabs.onChange((index: number) => {
                this.currentIndex = index;
            });
            Tabs.barWidth(286);
            Tabs.barHeight(45);
            Tabs.width('100%');
            Tabs.height('100%');
            Tabs.scrollable(true);
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.TabMyContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '我的', 0);
                } });
            TabContent.align(Alignment.TopStart);
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(187:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('首页的内容');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(194:13)", "entry");
                }, Text);
                Text.pop();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '云村', 1);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(193:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('推荐的内容');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(199:13)", "entry");
                    Text.fontSize(35);
                    Text.fontColor('#888');
                }, Text);
                Text.pop();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '推荐', 2);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(198:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Scroll.create();
                    Scroll.debugLine("entry/src/main/ets/pages/IndexPage.ets(204:13)", "entry");
                }, Scroll);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(205:15)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Swiper.create(this.swiperController);
                    Swiper.debugLine("entry/src/main/ets/pages/IndexPage.ets(206:17)", "entry");
                    Swiper.width(349);
                    Swiper.height(145);
                    Swiper.loop(true);
                    Swiper.autoPlay(true);
                }, Swiper);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(207:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(210:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(213:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                Swiper.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceAround, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(221:17)", "entry");
                    Flex.height(98);
                    Flex.margin({ top: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(222:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(223:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('每日推荐');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(227:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(229:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(230:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('歌单');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(234:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(236:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(237:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('排行榜');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(241:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(243:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(244:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('电台');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(248:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(253:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(254:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('推荐歌单');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(255:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('歌单广场');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(259:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceAround });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(267:19)", "entry");
                    Flex.margin({ top: 13 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(268:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/LIhxL7p5UkYX5NmHL8vhzQ==/18869818556322650.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(269:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('听一首把自己感动的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(272:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(276:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/zpmOTZw533nltCQJtXC8Rg==/109951168215962168.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(277:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('我知道风里有诗，那正是民谣的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(280:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(284:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(285:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('[国电新势力] 网易电子音乐人精选');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(288:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(292:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/LIhxL7p5UkYX5NmHL8vhzQ==/18869818556322650.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(293:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('听一首把自己感动的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(296:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(300:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/zpmOTZw533nltCQJtXC8Rg==/109951168215962168.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(301:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('我知道风里有诗，那正是民谣的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(304:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(308:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(309:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('[国电新势力] 网易电子音乐人精选');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(312:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(320:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(321:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('新碟');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(322:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('更多新碟');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(326:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceAround });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(334:19)", "entry");
                    Flex.margin({ top: 13 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(335:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/Ik30xAtj2MQgCU5jE6K6Ww==/109951169052034633.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(336:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('暗流');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(339:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(343:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/UGP6oPJ35Ab_mvBljtzoPA==/109951169067369971.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(344:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('도화');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(347:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(351:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/AWcDI5wc9fkS2bZt6wIm-Q==/109951163212638897.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(352:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('Practice Make Perfect');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(355:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(363:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(364:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('最新MV');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(365:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('查看更多');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(369:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(377:19)", "entry");
                    Column.borderRadius({ topLeft: 12, topRight: 12 });
                    Column.backgroundColor('#efefef');
                    Column.alignItems(HorizontalAlign.Start);
                    Column.width(337);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(378:21)", "entry");
                    Image.width(337);
                    Image.height(213);
                    Image.margin({ top: 9, bottom: 4 });
                    Image.borderRadius(12);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('덤더럼(Dumhdurum)');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(383:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                    Text.height(42);
                    Text.margin({ left: 12 });
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(395:19)", "entry");
                    Flex.padding({ left: 20, right: 20 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(396:21)", "entry");
                    Row.alignItems(VerticalAlign.Center);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777285, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(397:23)", "entry");
                    Image.width(17);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('220000');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(400:23)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.margin({ left: 2, right: 4 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777277, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(404:23)", "entry");
                    Image.width(17);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('320000');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(407:23)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.margin({ left: 2 });
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777283, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(413:21)", "entry");
                    Image.width(12);
                    Image.height(14);
                }, Image);
                Flex.pop();
                Column.pop();
                Column.pop();
                Scroll.pop();
            });
            TabContent.height('100%');
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '发现', 3);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(203:11)", "entry");
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // .backgroundColor('red')
            Image.create({ "id": 16777276, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(435:9)", "entry");
            // .backgroundColor('red')
            Image.width(22);
            // .backgroundColor('red')
            Image.height(22);
            // .backgroundColor('red')
            Image.position({
                x: 328,
                y: 14
            });
        }, Image);
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "IndexPage";
    }
}
registerNamedRoute(() => new IndexPage(undefined, {}), "", { bundleName: "com.example.neteasy_cloud", moduleName: "entry", pagePath: "pages/IndexPage", pageFullPath: "entry/src/main/ets/pages/IndexPage", integratedHsp: "false", moduleType: "followWithHap" });
