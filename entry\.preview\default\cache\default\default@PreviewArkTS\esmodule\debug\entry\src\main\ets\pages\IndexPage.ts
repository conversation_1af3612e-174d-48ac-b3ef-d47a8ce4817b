if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface IndexPage_Params {
    titleParam?: Resource;
    controller?: TabsController;
    swiperController?: SwiperController;
    currentIndex?: number;
}
import router from "@ohos:router";
import { CommonConstants } from "@bundle:com.example.neteasy_cloud/entry/ets/common/constants/CommonConstants";
class IndexPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.titleParam = { "id": 16777231, "type": 10003, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" };
        this.controller = new TabsController();
        this.swiperController = new SwiperController();
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: IndexPage_Params) {
        if (params.titleParam !== undefined) {
            this.titleParam = params.titleParam;
        }
        if (params.controller !== undefined) {
            this.controller = params.controller;
        }
        if (params.swiperController !== undefined) {
            this.swiperController = params.swiperController;
        }
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
    }
    updateStateVars(params: IndexPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private titleParam: Resource;
    private controller: TabsController;
    private swiperController: SwiperController;
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    aboutToAppear() {
        if (router.getParams()) {
            this.titleParam = router.getParams()[CommonConstants.KEY_PARAM_DATA] as Resource;
        }
    }
    TabTextBuilder(text: string, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(text);
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(23:5)", "entry");
            Text.fontSize(this.currentIndex === index ? 18 : 16);
            Text.fontColor(this.currentIndex === index ? '#000' : '#888');
            Text.fontWeight(this.currentIndex === index ? 800 : 400);
        }, Text);
        Text.pop();
    }
    TabMyContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/IndexPage.ets(30:5)", "entry");
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(31:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceAround, alignItems: ItemAlign.Center });
            Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(32:9)", "entry");
            Flex.height(86);
        }, Flex);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(33:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(34:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('私人FM');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(38:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(40:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(41:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('心动模式');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(45:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(47:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(48:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('私人电台');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(52:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(54:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(55:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('跑步FM');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(59:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(61:11)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(62:13)", "entry");
            Image.width(38);
            Image.height(38);
            Image.margin({ bottom: 9 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('心动模式');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(66:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        Column.pop();
        Flex.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(70:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(71:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(72:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('本地音乐');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(75:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(85:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(86:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近播放');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(89:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(99:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(100:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('下载管理');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(103:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(113:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(114:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的电台');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(117:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(127:11)", "entry");
            Row.width('100%');
            Row.height(47);
            Row.alignItems(VerticalAlign.Center);
            Row.border({ width: { bottom: 1 }, color: '#dcdcdc', });
            Row.padding({ left: 13 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777280, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(128:13)", "entry");
            Image.width(21);
            Image.height(25);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的收藏');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(131:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.margin({ left: 8 });
        }, Text);
        Text.pop();
        Row.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/IndexPage.ets(142:9)", "entry");
            Divider.strokeWidth(9);
            Divider.color('#dcdcdc');
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(145:9)", "entry");
            Column.width('100%');
            Column.padding({ left: 13, top: 6 });
            Column.alignItems(HorizontalAlign.Start);
            Column.justifyContent(FlexAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('创建的歌单');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(146:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#000');
            Text.fontWeight(800);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(151:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create('http://p1.music.126.net/mENtBPo_dYhMgsv0_3ae9A==/109951165448009459.jpg');
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(152:13)", "entry");
            Image.width(56);
            Image.height(56);
            Image.margin({ right: 12 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(156:13)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('linwu-hi喜欢的音乐');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(157:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#000');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('14首');
            Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(160:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(177:5)", "entry");
            Column.justifyContent(FlexAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777227, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(178:9)", "entry");
            Image.width(22);
            Image.height(22);
            Image.position({
                x: 10,
                y: 14
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({ index: this.currentIndex, controller: this.controller });
            Tabs.debugLine("entry/src/main/ets/pages/IndexPage.ets(185:9)", "entry");
            Tabs.onChange((index: number) => {
                this.currentIndex = index;
            });
            Tabs.barWidth(286);
            Tabs.barHeight(45);
            Tabs.width('100%');
            Tabs.height('100%');
            Tabs.scrollable(true);
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.TabMyContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '我的', 0);
                } });
            TabContent.align(Alignment.TopStart);
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(186:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('首页的内容');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(193:13)", "entry");
                }, Text);
                Text.pop();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '云村', 1);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(192:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('推荐的内容');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(198:13)", "entry");
                    Text.fontSize(35);
                    Text.fontColor('#888');
                }, Text);
                Text.pop();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '推荐', 2);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(197:11)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Scroll.create();
                    Scroll.debugLine("entry/src/main/ets/pages/IndexPage.ets(203:13)", "entry");
                }, Scroll);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(204:15)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Swiper.create(this.swiperController);
                    Swiper.debugLine("entry/src/main/ets/pages/IndexPage.ets(205:17)", "entry");
                    Swiper.width(349);
                    Swiper.height(145);
                    Swiper.loop(true);
                    Swiper.autoPlay(true);
                }, Swiper);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(206:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(209:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('http://p1.music.126.net/lP1H_mfrmEeO0A9_oi-4WA==/109951169072752079.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(212:19)", "entry");
                    Image.width(349);
                    Image.height(145);
                }, Image);
                Swiper.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceAround, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(220:17)", "entry");
                    Flex.height(98);
                    Flex.margin({ top: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(221:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(222:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('每日推荐');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(226:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(228:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(229:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('歌单');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(233:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(235:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(236:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('排行榜');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(240:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(242:19)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777284, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(243:21)", "entry");
                    Image.width(43);
                    Image.height(43);
                    Image.margin({ bottom: 9 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('电台');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(247:21)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(252:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(253:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('推荐歌单');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(254:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('歌单广场');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(258:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceAround });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(266:19)", "entry");
                    Flex.margin({ top: 13 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(267:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/LIhxL7p5UkYX5NmHL8vhzQ==/18869818556322650.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(268:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('听一首把自己感动的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(271:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(275:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/zpmOTZw533nltCQJtXC8Rg==/109951168215962168.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(276:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('我知道风里有诗，那正是民谣的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(279:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(283:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(284:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('[国电新势力] 网易电子音乐人精选');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(287:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(291:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/LIhxL7p5UkYX5NmHL8vhzQ==/18869818556322650.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(292:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('听一首把自己感动的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(295:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(299:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/zpmOTZw533nltCQJtXC8Rg==/109951168215962168.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(300:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('我知道风里有诗，那正是民谣的歌');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(303:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(307:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(308:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('[国电新势力] 网易电子音乐人精选');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(311:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(319:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(320:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('新碟');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(321:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('更多新碟');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(325:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceAround });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(333:19)", "entry");
                    Flex.margin({ top: 13 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(334:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/Ik30xAtj2MQgCU5jE6K6Ww==/109951169052034633.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(335:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('暗流');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(338:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(342:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/UGP6oPJ35Ab_mvBljtzoPA==/109951169067369971.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(343:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('도화');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(346:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(350:21)", "entry");
                    Column.width(107);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/AWcDI5wc9fkS2bZt6wIm-Q==/109951163212638897.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(351:23)", "entry");
                    Image.width(107);
                    Image.margin({ bottom: 4 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('Practice Make Perfect');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(354:23)", "entry");
                    Text.fontSize(12);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Column.pop();
                Flex.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(362:17)", "entry");
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(363:19)", "entry");
                    Flex.padding({ left: 9, right: 9 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('最新MV');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(364:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('查看更多');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(368:21)", "entry");
                    Text.padding({ top: 2, right: 6, bottom: 2, left: 2 });
                    Text.border({ width: 1, color: '#ccc' });
                    Text.borderRadius(18);
                    Text.fontSize(10);
                    Text.fontColor('#000');
                }, Text);
                Text.pop();
                Flex.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/IndexPage.ets(376:19)", "entry");
                    Column.borderRadius({ topLeft: 12, topRight: 12 });
                    Column.backgroundColor('#efefef');
                    Column.alignItems(HorizontalAlign.Start);
                    Column.width(337);
                    Column.margin({ bottom: 13 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create('https://p1.music.126.net/qA0W0aER7LxB99iTOMQZcA==/109951166153828602.jpg');
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(377:21)", "entry");
                    Image.width(337);
                    Image.height(213);
                    Image.margin({ top: 9, bottom: 4 });
                    Image.borderRadius(12);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('덤더럼(Dumhdurum)');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(382:21)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.fontWeight(800);
                    Text.height(42);
                    Text.margin({ left: 12 });
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Flex.create({ direction: FlexDirection.Row, justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center });
                    Flex.debugLine("entry/src/main/ets/pages/IndexPage.ets(394:19)", "entry");
                    Flex.padding({ left: 20, right: 20 });
                }, Flex);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/IndexPage.ets(395:21)", "entry");
                    Row.alignItems(VerticalAlign.Center);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777285, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(396:23)", "entry");
                    Image.width(17);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('220000');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(399:23)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.margin({ left: 2, right: 4 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777277, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(403:23)", "entry");
                    Image.width(17);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('320000');
                    Text.debugLine("entry/src/main/ets/pages/IndexPage.ets(406:23)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#000');
                    Text.margin({ left: 2 });
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777283, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
                    Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(412:21)", "entry");
                    Image.width(12);
                    Image.height(14);
                }, Image);
                Flex.pop();
                Column.pop();
                Column.pop();
                Scroll.pop();
            });
            TabContent.height('100%');
            TabContent.tabBar({ builder: () => {
                    this.TabTextBuilder.call(this, '发现', 3);
                } });
            TabContent.debugLine("entry/src/main/ets/pages/IndexPage.ets(202:11)", "entry");
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // .backgroundColor('red')
            Image.create({ "id": 16777276, "type": 20000, params: [], "bundleName": "com.example.neteasy_cloud", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/IndexPage.ets(434:9)", "entry");
            // .backgroundColor('red')
            Image.width(22);
            // .backgroundColor('red')
            Image.height(22);
            // .backgroundColor('red')
            Image.position({
                x: 328,
                y: 14
            });
        }, Image);
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "IndexPage";
    }
}
registerNamedRoute(() => new IndexPage(undefined, {}), "", { bundleName: "com.example.neteasy_cloud", moduleName: "entry", pagePath: "pages/IndexPage", pageFullPath: "entry/src/main/ets/pages/IndexPage", integratedHsp: "false", moduleType: "followWithHap" });
