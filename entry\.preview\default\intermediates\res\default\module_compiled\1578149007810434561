   ability_desc   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	       $   This ability loads CategoryListPage.
   ability_label   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	          Category List Ability   detail_default_title   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	       
   Default title   list_item_summary   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	       
   Right text   list_item_title   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	          Single list   module_desc   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	       8   This module template implements Category List functions.
   page_title   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	          Title	   sub_title   baseT   D:\Code\harmony\open_neteasy_cloud\entry\src\main\resources\base\element\string.json	          Subtitle