/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ABILITY_DESC = 0x0100000d;
const int32_t STRING_ABILITY_LABEL = 0x0100000e;
const int32_t STRING_APP_NAME = 0x01000001;
const int32_t STRING_DETAIL_DEFAULT_TITLE = 0x0100000f;
const int32_t STRING_LIST_ITEM_SUMMARY = 0x01000010;
const int32_t STRING_LIST_ITEM_TITLE = 0x01000011;
const int32_t STRING_MODULE_DESC = 0x01000012;
const int32_t STRING_PAGE_TITLE = 0x01000013;
const int32_t STRING_SUB_TITLE = 0x01000014;
const int32_t COLOR_LIST_DIVIDER = 0x01000003;
const int32_t COLOR_LIST_ITEM_SUMMARY = 0x01000004;
const int32_t COLOR_LIST_ITEM_TITLE = 0x01000005;
const int32_t COLOR_PAGE_TITLE = 0x01000006;
const int32_t COLOR_SEARCH_PLACEHOLDER = 0x01000007;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x01000008;
const int32_t COLOR_THEME_BACKGROUND = 0x01000009;
const int32_t FLOAT_COLUMN_MARGIN_LEFT = 0x01000016;
const int32_t FLOAT_COLUMN_MARGIN_RIGHT = 0x01000017;
const int32_t FLOAT_DETAIL_LIST_RADIUS = 0x01000018;
const int32_t FLOAT_DETAIL_TITLE_HEIGHT = 0x01000019;
const int32_t FLOAT_DETAIL_TITLE_SIZE = 0x0100001a;
const int32_t FLOAT_DIVIDER_HEIGHT = 0x0100001b;
const int32_t FLOAT_GRID_ROW_MARGIN_LEFT = 0x0100001c;
const int32_t FLOAT_GRID_ROW_MARGIN_RIGHT = 0x0100001d;
const int32_t FLOAT_IMAGE_MARGIN_LEFT = 0x0100001e;
const int32_t FLOAT_IMAGE_MARGIN_RIGHT = 0x0100001f;
const int32_t FLOAT_IMAGE_RIGHT_ARROW_HEIGHT = 0x01000020;
const int32_t FLOAT_IMAGE_RIGHT_ARROW_WIDTH = 0x01000021;
const int32_t FLOAT_IMAGE_SIZE = 0x01000022;
const int32_t FLOAT_ITEM_TEXT_MARGIN_LEFT = 0x01000023;
const int32_t FLOAT_ITEM_TEXT_MARGIN_RIGHT = 0x01000024;
const int32_t FLOAT_LIST_DIVIDER_MARGIN = 0x01000025;
const int32_t FLOAT_LIST_ITEM_HEIGHT = 0x01000026;
const int32_t FLOAT_LIST_MARGIN_TOP = 0x01000027;
const int32_t FLOAT_LIST_PADDING_BOTTOM = 0x01000028;
const int32_t FLOAT_LIST_PADDING_TOP = 0x01000029;
const int32_t FLOAT_SCROLL_BOTTOM_MARGIN_TOP = 0x0100002a;
const int32_t FLOAT_SEARCH_HEIGHT = 0x0100002b;
const int32_t FLOAT_SEARCH_MARGIN_BOTTOM = 0x0100002c;
const int32_t FLOAT_SEARCH_MARGIN_TOP = 0x0100002d;
const int32_t FLOAT_SEARCH_RADIUS = 0x0100002e;
const int32_t FLOAT_SEARCH_TEXT_SIZE = 0x0100002f;
const int32_t FLOAT_SUB_ITEM_MARGIN_RIGHT = 0x01000030;
const int32_t FLOAT_SUB_TITLE_HEIGHT = 0x01000031;
const int32_t FLOAT_SUB_TITLE_LEFT_SIZE = 0x01000032;
const int32_t FLOAT_SUB_TITLE_MARGIN_LEFT = 0x01000033;
const int32_t FLOAT_SUB_TITLE_RIGHT_SIZE = 0x01000034;
const int32_t FLOAT_SUB_TITLE_SIZE = 0x01000035;
const int32_t FLOAT_TEXT_PADDING_BOTTOM = 0x01000036;
const int32_t FLOAT_TITLE_FONT_SIZE = 0x01000037;
const int32_t FLOAT_TITLE_HEIGHT = 0x01000038;
const int32_t FLOAT_TITLE_MARGIN_LEFT = 0x01000039;
const int32_t FLOAT_TITLE_MARGIN_TOP = 0x0100003a;
const int32_t MEDIA_APP_ICON = 0x01000000;
const int32_t MEDIA_COMMENT = 0x0100003d;
const int32_t MEDIA_IC_BACK = 0x0100000c;
const int32_t MEDIA_IC_RIGHT_ARROW = 0x0100003f;
const int32_t MEDIA_ICON = 0x0100000a;
const int32_t MEDIA_LIKE = 0x01000045;
const int32_t MEDIA_LODING = 0x01000015;
const int32_t MEDIA_LOGO = 0x0100003b;
const int32_t MEDIA_MENU = 0x0100000b;
const int32_t MEDIA_MORE_MENU = 0x01000043;
const int32_t MEDIA_MUSIC = 0x01000002;
const int32_t MEDIA_MUSIC_ICON = 0x01000040;
const int32_t MEDIA_MY_NAV_ICON1 = 0x0100003e;
const int32_t MEDIA_NAV_ICON1 = 0x01000044;
const int32_t MEDIA_SEARCH = 0x0100003c;
const int32_t MEDIA_TIMG = 0x01000042;
const int32_t PROFILE_MAIN_PAGES = 0x01000041;
}
#endif